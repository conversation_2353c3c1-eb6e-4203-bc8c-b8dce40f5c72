import { Tabs } from 'antd';
import React from 'react';
import DistributionComponent from './Distribution';
import OrderComponent from './Order';
import TypeComponent from './Type';

const CouponPage: React.FC = () => {
  const items = [
    {
      key: 'type',
      label: '代金券类型维护',
      children: <TypeComponent />,
    },
    {
      key: 'distribution',
      label: '代金券发放',
      children: <DistributionComponent />,
    },
    {
      key: 'order',
      label: '代金券订单管理',
      children: <OrderComponent />,
    },
  ];

  return <Tabs size="large" tabBarStyle={{ marginBottom: 24 }} items={items} />;
};

export default CouponPage;
