import ProFormImg from '@/components/ProFormItem/ProFormImg';
import MyEditor, { WangEditorRef } from '@/components/WangEditor';
import { DictionarieState } from '@/models/dictionarie';
import { index } from '@/services/service-type';
import {
  DrawerForm,
  ProFormDigit,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Col, Divider, Form, message, Row } from 'antd';
import React, { useEffect, useRef } from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Service;
  onSave: (info: API.Service) => Promise<void>;
  onClose: () => void;
  dictionarie: DictionarieState;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
  dictionarie,
}) => {
  const [formRef] = Form.useForm<API.ServiceType>();
  const editorRef = useRef<WangEditorRef>(null);

  // 当模态框打开时重置表单
  useEffect(() => {
    if (open) {
      if (info) {
        // 编辑模式：设置表单值
        formRef.setFieldsValue(info);
      } else {
        // 新增模式：重置表单
        formRef.resetFields();
      }
    }
  }, [open, info, formRef]);

  return (
    <DrawerForm<API.Service>
      form={formRef}
      title={info ? '编辑服务项' : '注册服务项'}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
        onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
    >
      <ProFormText name="id" label="ID" hidden />
      <Row gutter={[16, 16]} style={{ width: '100%' }}>
        <Col span={16}>
          <ProFormSelect
            name="serviceTypeId"
            label="服务品牌"
            request={async () => {
              const { errCode, msg, data } = await index({});
              if (errCode) {
                message.error(msg || '服务品牌查询失败');
                return [];
              }
              return (data?.list || []).map((item) => ({
                label: item.name,
                value: item.id,
              }));
            }}
            rules={[{ required: true, message: '请选择服务品牌！' }]}
          />
          <ProFormText
            name="serviceName"
            label="服务名称"
            rules={[{ required: true, message: '请输入服务名称！' }]}
          />
        </Col>
        <Col span={8}>
          <ProFormImg
            name="logo"
            label="服务logo"
            rules={[{ required: true, message: '请上传服务logo！' }]}
          />
        </Col>
      </Row>

      <ProFormDigit
        name="basePrice"
        label="基础价格"
        rules={[{ required: true, message: '请输入基础价格！' }]}
        colProps={{ span: 12 }}
        min={0}
        fieldProps={{ precision: 2 }}
      />
      <ProFormSelect
        name="petTypes"
        label="宠物类型"
        rules={[{ required: true, message: '请选择宠物类型！' }]}
        colProps={{ span: 12 }}
        options={[
          { label: '猫', value: 'cat' },
          { label: '狗', value: 'dog' },
        ]}
      />
      <ProFormSelect
        name="size"
        label="适用体型"
        colProps={{ span: 12 }}
        options={dictionarie.list
          .filter((d) => d.type === '宠物体型')
          .map((item) => ({
            label: item.name,
            value: item.code,
          }))}
      />
      <ProFormSelect
        name="hairType"
        label="毛发类型"
        colProps={{ span: 12 }}
        options={[
          { label: '短毛', value: 'short' },
          { label: '长毛', value: 'long' },
        ]}
      />
      <ProFormSwitch
        name="distanceChargeFlag"
        label="按距离计费"
        colProps={{ span: 12 }}
      />
      <ProFormSwitch
        name="cardDiscountFlag"
        label="支持权益卡"
        colProps={{ span: 12 }}
      />
      <ProFormDigit
        name="orderIndex"
        label="排序值"
        rules={[{ required: true, message: '请输入排序值！' }]}
        colProps={{ span: 12 }}
        min={0}
      />
      <ProFormTextArea
        name="description"
        label="服务描述"
        colProps={{ span: 24 }}
        hidden
      />
      <Divider>描述</Divider>
      <MyEditor
        ref={editorRef}
        value={info?.description || ''}
        onChange={(content: string) => {
          formRef.setFieldsValue({
            description: content,
          });
        }}
        style={{ minHeight: '300px' }}
      />
    </DrawerForm>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(EditModal);
