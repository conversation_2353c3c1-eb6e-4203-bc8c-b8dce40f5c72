import { request } from '@umijs/max';

/** 查询列表  GET /customers */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: API.Customer[] }>>(
    '/customers',
    {
      method: 'GET',
      params,
    },
  );
}

/** 创建  POST /customers */
export async function create(body: Omit<API.Customer, 'id'>) {
  return request<API.ResType<API.Customer>>('/customers', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 按ID查询  GET /customers/:id */
export async function show(id: number) {
  return request<API.ResType<API.Customer>>(`/customers/${id}`, {
    method: 'GET',
  });
}

/** 修改  PUT /customers/:id */
export async function update(id: number, body: Partial<API.Customer>) {
  return request<API.ResType<unknown>>(`/customers/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}
/** 修改客户启用禁用状态  PUT /customers/:id/status */
export async function updateStatus(id: number, body: { status: number }) {
  return request<API.ResType<unknown>>(`/customers/${id}/status`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除  DELETE /customers/:id */
export async function remove(id: number) {
  return request<API.ResType<unknown>>(`/customers/${id}`, {
    method: 'DELETE',
  });
}
